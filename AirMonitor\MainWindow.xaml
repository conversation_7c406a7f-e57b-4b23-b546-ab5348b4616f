﻿<Window x:Class="AirMonitor.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:AirMonitor"
        xmlns:controls="clr-namespace:AirMonitor.Controls"
        mc:Ignorable="d"
        Title="AirMonitor - Fluent Design 主题演示"
        Height="600" Width="900"
        Background="{DynamicResource ApplicationBackgroundBrush}"
        WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0"
                Background="{DynamicResource LayerBackgroundBrush}"
                BorderBrush="{DynamicResource DividerStrokeColorDefaultBrush}"
                BorderThickness="0,0,0,1"
                Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="AirMonitor"
                               FontSize="24"
                               FontWeight="SemiBold"
                               Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                               VerticalAlignment="Center"/>
                    <TextBlock Text="Fluent Design 色彩系统演示"
                               FontSize="14"
                               Foreground="{DynamicResource TextFillColorSecondaryBrush}"
                               VerticalAlignment="Center"
                               Margin="15,0,0,0"/>
                </StackPanel>

                <!-- 主题切换按钮 -->
                <controls:ThemeToggleButton Grid.Column="1"/>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <ScrollViewer Grid.Row="1"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Padding="20">
            <StackPanel>

                <!-- 色彩展示卡片 -->
                <Border Background="{DynamicResource CardBackgroundBrush}"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="20"
                        Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="主色调 (Primary Colors)"
                                   FontSize="18"
                                   FontWeight="SemiBold"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   Margin="0,0,0,15"/>

                        <UniformGrid Columns="3"
                                     HorizontalAlignment="Left">
                            <!-- 主色调示例 -->
                            <Border Background="{DynamicResource SystemAccentBrush}"
                                    Width="100" Height="60"
                                    CornerRadius="4" Margin="5">
                                <TextBlock Text="Primary"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                            </Border>
                            <Border Background="{DynamicResource SystemAccentBrushSecondary}"
                                    Width="100" Height="60"
                                    CornerRadius="4" Margin="5">
                                <TextBlock Text="Secondary"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                            </Border>
                            <Border Background="{DynamicResource SystemAccentBrushTertiary}"
                                    Width="100" Height="60"
                                    CornerRadius="4" Margin="5">
                                <TextBlock Text="Tertiary"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- 语义色展示卡片 -->
                <Border Background="{DynamicResource CardBackgroundBrush}"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="20"
                        Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="语义色 (Semantic Colors)"
                                   FontSize="18"
                                   FontWeight="SemiBold"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   Margin="0,0,0,15"/>

                        <UniformGrid Columns="4"
                                     HorizontalAlignment="Left">
                            <!-- 成功色 -->
                            <Border Background="{DynamicResource SystemFillColorSuccessBrush}"
                                    Width="80" Height="50"
                                    CornerRadius="4" Margin="5">
                                <TextBlock Text="Success"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontSize="12"/>
                            </Border>
                            <!-- 警告色 -->
                            <Border Background="{DynamicResource SystemFillColorWarningBrush}"
                                    Width="80" Height="50"
                                    CornerRadius="4" Margin="5">
                                <TextBlock Text="Warning"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontSize="12"/>
                            </Border>
                            <!-- 错误色 -->
                            <Border Background="{DynamicResource SystemFillColorCriticalBrush}"
                                    Width="80" Height="50"
                                    CornerRadius="4" Margin="5">
                                <TextBlock Text="Error"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontSize="12"/>
                            </Border>
                            <!-- 信息色 -->
                            <Border Background="{DynamicResource SystemFillColorNeutralBrush}"
                                    Width="80" Height="50"
                                    CornerRadius="4" Margin="5">
                                <TextBlock Text="Info"
                                           Foreground="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontSize="12"/>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- 控件演示卡片 -->
                <Border Background="{DynamicResource CardBackgroundBrush}"
                        BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="20">
                    <StackPanel>
                        <TextBlock Text="控件演示 (Control Examples)"
                                   FontSize="18"
                                   FontWeight="SemiBold"
                                   Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                   Margin="0,0,0,15"/>

                        <WrapPanel Orientation="Horizontal">
                            <!-- 按钮示例 -->
                            <Button Content="Primary Button"
                                    Background="{DynamicResource SystemAccentBrush}"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Padding="15,8"
                                    Margin="5"/>

                            <Button Content="Secondary Button"
                                    Background="{DynamicResource ControlFillColorDefaultBrush}"
                                    Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                    BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                    BorderThickness="1"
                                    Padding="15,8"
                                    Margin="5"/>

                            <!-- 文本框示例 -->
                            <TextBox Text="示例文本"
                                     Background="{DynamicResource ControlFillColorInputActiveBrush}"
                                     Foreground="{DynamicResource TextFillColorPrimaryBrush}"
                                     BorderBrush="{DynamicResource ControlStrokeColorDefaultBrush}"
                                     BorderThickness="1"
                                     Padding="10,8"
                                     Margin="5"
                                     Width="120"/>
                        </WrapPanel>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
