{"Version": 1, "WorkspaceRootPath": "D:\\项目\\00 AirMonitor\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\converters\\menuconverters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\converters\\menuconverters.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\controls\\fluentmenuitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\controls\\fluentmenuitem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\controls\\fluentmenubar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\controls\\fluentmenubar.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\styles\\fonts.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\styles\\fonts.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|d:\\项目\\00 airmonitor\\airmonitor\\controls\\themetogglebutton.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{06B8D57B-FAD8-44CE-A7D7-48FF404A1CD5}|AirMonitor\\AirMonitor.csproj|solutionrelative:airmonitor\\controls\\themetogglebutton.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "MenuConverters.cs", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\Converters\\MenuConverters.cs", "RelativeDocumentMoniker": "AirMonitor\\Converters\\MenuConverters.cs", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\Converters\\MenuConverters.cs", "RelativeToolTip": "AirMonitor\\Converters\\MenuConverters.cs", "ViewState": "AgIAAG8AAAAAAAAAAAA6wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T12:54:23.842Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "FluentMenuItem.cs", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\FluentMenuItem.cs", "RelativeDocumentMoniker": "AirMonitor\\Controls\\FluentMenuItem.cs", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\FluentMenuItem.cs", "RelativeToolTip": "AirMonitor\\Controls\\FluentMenuItem.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T12:53:58.939Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "FluentMenuBar.cs", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\FluentMenuBar.cs", "RelativeDocumentMoniker": "AirMonitor\\Controls\\FluentMenuBar.cs", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\FluentMenuBar.cs", "RelativeToolTip": "AirMonitor\\Controls\\FluentMenuBar.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-21T12:53:55.6Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Fonts.xaml", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\Styles\\Fonts.xaml", "RelativeDocumentMoniker": "AirMonitor\\Styles\\Fonts.xaml", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\Styles\\Fonts.xaml", "RelativeToolTip": "AirMonitor\\Styles\\Fonts.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T12:46:05.805Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeDocumentMoniker": "AirMonitor\\MainWindow.xaml", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\MainWindow.xaml", "RelativeToolTip": "AirMonitor\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T12:35:39.591Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ThemeToggleButton.xaml", "DocumentMoniker": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\ThemeToggleButton.xaml", "RelativeDocumentMoniker": "AirMonitor\\Controls\\ThemeToggleButton.xaml", "ToolTip": "D:\\项目\\00 AirMonitor\\AirMonitor\\Controls\\ThemeToggleButton.xaml", "RelativeToolTip": "AirMonitor\\Controls\\ThemeToggleButton.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-21T12:35:29.995Z", "EditorCaption": ""}]}]}]}